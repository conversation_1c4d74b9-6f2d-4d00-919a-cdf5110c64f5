@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Revantad Store Brand Colors - Green and Mustard */
  --primary-green: #22c55e;
  --primary-green-dark: #16a34a;
  --secondary-mustard: #facc15;
  --secondary-mustard-dark: #eab308;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary-green: #15803d;
    --secondary-mustard: #a16207;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
}

html {
  scroll-behavior: smooth;
}

/* Loading Skeleton Animation */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Revantad Store Custom Component Classes */
.btn-primary {
  @apply bg-green-500 hover:bg-green-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-yellow-400 hover:bg-yellow-500 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5;
}

.btn-outline {
  @apply border-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200;
}

.card {
  @apply bg-white dark:bg-slate-800 rounded-xl shadow-md border border-gray-100 dark:border-slate-700;
}

.hero-gradient {
  @apply bg-gradient-to-br from-green-500 via-green-600 to-yellow-400;
}

/* Theme-aware text gradient with optimal visibility */
.text-gradient {
  /* Light mode: Darker gradient with high contrast */
  @apply bg-gradient-to-r from-green-700 to-green-600 bg-clip-text text-transparent;
  /* Fallback color for accessibility */
  color: #15803d;
}

.dark .text-gradient {
  /* Dark mode: Brighter gradient with vibrant colors */
  @apply bg-gradient-to-r from-green-400 to-yellow-400 bg-clip-text text-transparent;
  /* Fallback color for accessibility */
  color: #4ade80;
}

/* Enhanced visibility for critical brand text */
.text-gradient-enhanced {
  /* Light mode: Maximum contrast gradient */
  @apply bg-gradient-to-r from-green-800 to-green-700 bg-clip-text text-transparent;
  color: #14532d;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .text-gradient-enhanced {
  /* Dark mode: Bright, vibrant gradient */
  @apply bg-gradient-to-r from-green-300 to-yellow-300 bg-clip-text text-transparent;
  color: #86efac;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.glass-effect {
  @apply backdrop-blur-md bg-white/10 border border-white/20;
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-bounce-gentle {
  animation: bounceGentle 2s infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceGentle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(34, 197, 94, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Enhanced Dashboard Animations */
.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.animate-pulse-glow {
  animation: pulseGlow 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dashboard Card Enhancements */
.dashboard-card {
  @apply transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-card:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient Text Effects */
.text-gradient-blue {
  @apply bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent;
}

.text-gradient-green {
  @apply bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent;
}

.text-gradient-yellow {
  @apply bg-gradient-to-r from-yellow-600 to-yellow-400 bg-clip-text text-transparent;
}

.text-gradient-red {
  @apply bg-gradient-to-r from-red-600 to-red-400 bg-clip-text text-transparent;
}

/* Custom Scrollbar for Dashboard */
.main-content-scroll::-webkit-scrollbar {
  width: 6px;
}

.main-content-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.main-content-scroll::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(34, 197, 94, 0.5);
}

@keyframes moonGlow {
  0%, 100% {
    filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
    transform: scale(1);
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Moon Phase Enhancements */
.moon-phase-icon {
  transition: all 0.3s ease;
  cursor: pointer;
}

.moon-phase-icon:hover {
  animation: moonGlow 2s infinite;
}

.moon-phase-container {
  position: relative;
}

.calendar-day-cell {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.calendar-day-cell:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.calendar-day-cell:hover .moon-phase-icon {
  transform: scale(1.1);
}

.animate-fade-in-up {
  animation: fadeInUp 0.4s ease-out;
}

/* Bisaya-Tagalog Cultural Enhancements */
.bisaya-calendar {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.bisaya-text {
  letter-spacing: 0.025em;
  line-height: 1.6;
}

.cultural-accent {
  background: linear-gradient(135deg, #22c55e 0%, #facc15 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.hover-scale-102:hover {
  transform: scale(1.02);
}

.filipino-shadow {
  box-shadow: 0 4px 20px rgba(34, 197, 94, 0.15), 0 2px 8px rgba(250, 204, 21, 0.1);
}

.filipino-shadow:hover {
  box-shadow: 0 8px 30px rgba(34, 197, 94, 0.2), 0 4px 12px rgba(250, 204, 21, 0.15);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.4);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.6);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.6);
}

/* Sidebar specific scrollbar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.3);
}

.dark .sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.5);
}

/* Prevent text blurring on transforms */
.sidebar-nav-item {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Ensure crisp text rendering */
.crisp-text {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Ensure sidebar text is always visible */
.sidebar-text h3 {
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Light mode sidebar text fix */
.light .sidebar-text h3 {
  color: #1f2937 !important;
}

/* Dark mode sidebar text fix */
.dark .sidebar-text h3 {
  color: #f8fafc !important;
}

/* Force all sidebar navigation text to be visible in light mode */
.light .sidebar-text h3,
.light [data-sidebar] h3,
.light nav h3,
.light .sidebar-nav-item h3 {
  color: #1f2937 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  -webkit-text-fill-color: #1f2937 !important;
}

/* Specific fix for Settings and other nav items */
.light button h3,
.light .sidebar-text h3 {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
  text-shadow: none !important;
}

/* Global text visibility fixes for light mode */
.light * {
  /* Ensure no text is invisible in light mode */
}

/* Fix for any remaining light text in light mode */
.light .text-white {
  color: #1f2937 !important;
}

/* Ensure text gradients are always visible with fallback support */
.light .text-gradient,
.light .text-gradient-enhanced {
  /* Fallback for browsers that don't support bg-clip-text */
  color: #15803d !important;
}

.dark .text-gradient,
.dark .text-gradient-enhanced {
  /* Fallback for browsers that don't support bg-clip-text */
  color: #4ade80 !important;
}

/* Force visibility for brand text elements */
.light span[class*="text-gradient"],
.light h1[class*="text-gradient"],
.light h2[class*="text-gradient"] {
  color: #15803d !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix Product Lists page title visibility */
.light main h1 {
  color: #111827 !important;
  font-weight: 700 !important;
}

.light main p {
  color: #374151 !important;
}

/* Fix Product Card Text Visibility */
.light main h3[style*="#111827"] {
  color: #0f172a !important;
  font-weight: 700 !important;
}

.light main .font-bold {
  color: #0f172a !important;
  font-weight: 700 !important;
}

/* Product card description text */
.light main p[style*="#6b7280"] {
  color: #374151 !important;
}

/* Fix Product Card Interactive Elements */
.light .bg-white\/90 {
  background-color: rgba(255, 255, 255, 0.95) !important;
  color: #374151 !important;
}

.light .text-gray-600 {
  color: #374151 !important;
}

/* Product card three dots menu and checkbox */
.light button[title="Quick Actions"] {
  background-color: rgba(255, 255, 255, 0.95) !important;
  color: #374151 !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.light button[title="Quick Actions"]:hover {
  background-color: #ffffff !important;
  color: #111827 !important;
}

/* Fix Debt Management Card Text Visibility */
.light .text-gray-600 {
  color: #374151 !important;
}

.light .text-gray-500 {
  color: #4b5563 !important;
}

/* Debt card customer names and info */
.light h3[style*="#111827"] {
  color: #0f172a !important;
}

/* Debt card labels and secondary text */
.light span[style*="#6b7280"] {
  color: #374151 !important;
}

/* Fix Debt Card Buttons */
.light .bg-blue-100 {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.light .bg-orange-100 {
  background-color: #f97316 !important;
  color: #ffffff !important;
}

.light .bg-green-100 {
  background-color: #22c55e !important;
  color: #ffffff !important;
}

.light .text-blue-700 {
  color: #ffffff !important;
}

.light .text-orange-700 {
  color: #ffffff !important;
}

.light .text-green-700 {
  color: #ffffff !important;
}

/* Debt card numbers - make them darker and more visible */
.light .text-red-600 {
  color: #dc2626 !important;
  font-weight: 700 !important;
}

.light .text-green-600 {
  color: #16a34a !important;
  font-weight: 700 !important;
}

/* Family Gallery Card Text Fixes */
.light .text-gray-600 {
  color: #374151 !important;
}

.light .text-gray-500 {
  color: #4b5563 !important;
}

/* Family Gallery Category Badges */
.light .bg-blue-100.text-blue-800 {
  background-color: #3b82f6 !important;
  color: #ffffff !important;
}

.light .bg-green-100.text-green-800 {
  background-color: #22c55e !important;
  color: #ffffff !important;
}

.light .bg-yellow-100.text-yellow-800 {
  background-color: #eab308 !important;
  color: #ffffff !important;
}

.light .bg-amber-100.text-amber-800 {
  background-color: #f59e0b !important;
  color: #ffffff !important;
}

.light .bg-pink-100.text-pink-800 {
  background-color: #ec4899 !important;
  color: #ffffff !important;
}

.light .bg-orange-100.text-orange-800 {
  background-color: #f97316 !important;
  color: #ffffff !important;
}

/* Family Gallery Tags */
.light .bg-gray-100.text-gray-600 {
  background-color: #e5e7eb !important;
  color: #1f2937 !important;
}

/* Family Gallery Card Titles - Make them very dark and bold */
.light h3.font-semibold.text-gray-900 {
  color: #111827 !important;
  font-weight: 700 !important;
}

.light .text-gray-900 {
  color: #111827 !important;
}



.light .text-gray-100 {
  color: #1f2937 !important;
}

.light .text-slate-100 {
  color: #1f2937 !important;
}

/* Ensure tooltips remain readable */
.light .bg-gray-900 {
  background-color: #1f2937 !important;
}

.light .bg-gray-800 {
  background-color: #374151 !important;
}

/* Fix for any text that might be too light */
.light [class*="text-gray-1"] {
  color: #1f2937 !important;
}

.light [class*="text-slate-1"] {
  color: #1f2937 !important;
}

/* Comprehensive light mode text visibility fixes */
.light .text-gray-50,
.light .text-gray-100,
.light .text-gray-200,
.light .text-slate-50,
.light .text-slate-100,
.light .text-slate-200 {
  color: #1f2937 !important;
}

/* Ensure buttons with white text are readable */
.light button .text-white:not(.bg-green-500):not(.bg-blue-500):not(.bg-red-500):not(.bg-yellow-500):not(.bg-purple-500):not([class*="bg-gradient"]) {
  color: #1f2937 !important;
}

/* Fix for any remaining invisible text */
.light *[style*="color: #ffffff"],
.light *[style*="color: #f9fafb"],
.light *[style*="color: #f8fafc"] {
  color: #1f2937 !important;
}

/* Ensure all text elements have sufficient contrast in light mode */
.light h1, .light h2, .light h3, .light h4, .light h5, .light h6,
.light p, .light span, .light div, .light label, .light button {
  color: inherit;
}

/* Override any problematic light colors */
.light .text-opacity-90,
.light .text-opacity-80,
.light .text-opacity-70 {
  opacity: 1 !important;
}

/* Ultra-specific sidebar text visibility fix */
.light nav .sidebar-nav-item h3,
.light .sidebar-nav-item .sidebar-text h3,
.light button.sidebar-nav-item h3,
.light div.group button h3,
.light [class*="sidebar"] h3 {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  text-shadow: none !important;
  background: transparent !important;
}

/* Force visibility for all navigation text */
.light nav h3,
.light nav span,
.light nav div,
.light .sidebar-text * {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
}

/* Final comprehensive sidebar text visibility fix */
.light .sidebar-text h3,
.light nav .sidebar-text h3,
.light button .sidebar-text h3,
.light .sidebar-nav-item h3,
.light [class*="sidebar"] h3 {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  text-shadow: none !important;
  background: transparent !important;
  font-weight: 500 !important;
}

/* Specific fix for Settings menu item */
.light button[data-section="settings"] h3,
.light button[data-section="settings"] .sidebar-text h3,
.light .sidebar-nav-item:last-child h3 {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* Ensure crisp image rendering */
.crisp-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}

/* Profile picture specific optimizations - Facebook-like quality */
.profile-picture {
  image-rendering: auto;
  image-rendering: -webkit-optimize-contrast;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Ensure smooth scaling and high quality */
  -webkit-filter: none;
  filter: none;
  /* Prevent blur on transform */
  will-change: transform;
}

/* Customer debt profile pictures - extra crisp */
.customer-debt-profile {
  /* Force high quality rendering */
  image-rendering: auto !important;
  image-rendering: -webkit-optimize-contrast !important;
  image-rendering: optimize-contrast !important;

  /* Remove all blur effects */
  -webkit-filter: none !important;
  filter: none !important;
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;

  /* Prevent transform blur */
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  -webkit-transform: translateZ(0) scale(1) !important;
  transform: translateZ(0) scale(1) !important;

  /* Force crisp edges */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;

  /* Prevent any scaling blur */
  will-change: auto !important;
  contain: layout style paint !important;

  /* Override any potential Tailwind or other CSS */
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure modal containers don't blur child images */
.fixed.inset-0 img,
.fixed.inset-0 .customer-debt-profile {
  -webkit-filter: none !important;
  filter: none !important;
  image-rendering: auto !important;
}

/* Enhanced backdrop blur for sticky elements */
.backdrop-blur-enhanced {
  backdrop-filter: blur(8px) saturate(180%);
  -webkit-backdrop-filter: blur(8px) saturate(180%);
}

/* Smooth scrolling for sidebar navigation */
.sidebar-nav-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
}

/* Enhanced scrollbar for navigation area - Professional & Larger */
.sidebar-nav-scroll {
  scrollbar-width: auto;
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(243, 244, 246, 0.4);
  overflow-y: auto !important;
  overflow-x: hidden;
}

.sidebar-nav-scroll::-webkit-scrollbar {
  width: 14px;
  background: rgba(243, 244, 246, 0.5);
  border-radius: 7px;
  display: block !important;
}

.sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 7px;
  margin: 2px 0;
  border: 1px solid rgba(229, 231, 235, 0.6);
  min-height: 50px;
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border-radius: 7px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(34, 197, 94, 0.3);
  min-height: 30px;
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(16, 185, 129, 0.7) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.3);
  transform: scale(1.05);
}

.sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.4);
}

/* Dark theme enhanced scrollbar */
.dark .sidebar-nav-scroll {
  scrollbar-color: rgba(34, 197, 94, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.6);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.6);
  border: 1px solid rgba(71, 85, 105, 0.7);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.7) 0%, rgba(16, 185, 129, 0.6) 100%);
  border: 2px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(16, 185, 129, 0.8) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 8px rgba(34, 197, 94, 0.4);
}

.dark .sidebar-nav-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 1) 0%, rgba(16, 185, 129, 0.9) 100%);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.5);
}

/* Professional gradient overlays */
.gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-top {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.9), transparent);
}

.gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(249, 250, 251, 0.9), transparent);
  pointer-events: none;
}

.dark .gradient-overlay-bottom {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.9), transparent);
}

/* Professional Main Content Scrollbar */
.main-content-scroll {
  scroll-behavior: smooth;
  scrollbar-width: auto;
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(243, 244, 246, 0.4);
}

.main-content-scroll::-webkit-scrollbar {
  width: 12px;
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
}

.main-content-scroll::-webkit-scrollbar-track {
  background: rgba(243, 244, 246, 0.3);
  border-radius: 6px;
  margin: 4px 0;
  border: 1px solid rgba(229, 231, 235, 0.4);
}

.main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  min-height: 30px;
}

.main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.7) 0%, rgba(37, 99, 235, 0.6) 100%);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
  transform: scale(1.02);
}

.main-content-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.7) 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.4);
}

/* Dark theme for main content scrollbar */
.dark .main-content-scroll {
  scrollbar-color: rgba(59, 130, 246, 0.5) rgba(51, 65, 85, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar {
  background: rgba(51, 65, 85, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.5);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6) 0%, rgba(37, 99, 235, 0.5) 100%);
  border: 1px solid rgba(30, 41, 59, 0.3);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(37, 99, 235, 0.7) 100%);
  border-color: rgba(30, 41, 59, 0.4);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.4);
}

.dark .main-content-scroll::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.8) 100%);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.5);
}

/* Fade indicators for scrollable content */
.scroll-fade-top::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-top::before {
  background: linear-gradient(to bottom, rgba(30, 41, 59, 0.8), transparent);
}

.scroll-fade-bottom::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(249, 250, 251, 0.8), transparent);
  pointer-events: none;
  z-index: 5;
}

.dark .scroll-fade-bottom::after {
  background: linear-gradient(to top, rgba(15, 23, 42, 0.8), transparent);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-slate-800;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-200 hover:-translate-y-1;
}

.hover-glow {
  @apply transition-shadow duration-200 hover:shadow-lg hover:shadow-green-500/25;
}

/* AI Assistant Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.8), 0 0 30px rgba(139, 92, 246, 0.6);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes typing-dots {
  0%, 20% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  80%, 100% {
    transform: scale(1);
    opacity: 1;
  }
}

.ai-float {
  animation: float 3s ease-in-out infinite;
}

.ai-glow {
  animation: glow 2s ease-in-out infinite;
}

.ai-pulse-ring {
  animation: pulse-ring 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}

.typing-dot {
  animation: typing-dots 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Smooth scrollbar for chat */
.chat-scroll::-webkit-scrollbar {
  width: 4px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 2px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}

/* Professional Sukli Border Animation */
@keyframes border-travel {
  0% {
    border-image: linear-gradient(90deg, #3b82f6, transparent, transparent, transparent) 1;
  }
  25% {
    border-image: linear-gradient(180deg, transparent, #3b82f6, transparent, transparent) 1;
  }
  50% {
    border-image: linear-gradient(270deg, transparent, transparent, #3b82f6, transparent) 1;
  }
  75% {
    border-image: linear-gradient(0deg, transparent, transparent, transparent, #3b82f6) 1;
  }
  100% {
    border-image: linear-gradient(90deg, #3b82f6, transparent, transparent, transparent) 1;
  }
}

.sukli-border-animation {
  position: relative;
  border: 2px solid transparent;
  border-radius: 0.5rem;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(90deg, transparent, transparent, transparent, transparent) border-box;
  transition: all 0.3s ease;
}

.dark .sukli-border-animation {
  background: linear-gradient(#1e293b, #1e293b) padding-box,
              linear-gradient(90deg, transparent, transparent, transparent, transparent) border-box;
}

.sukli-border-animation:hover {
  animation: border-travel 2s linear infinite;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(90deg, #3b82f6, #3b82f6, #3b82f6, #3b82f6) border-box;
}

.dark .sukli-border-animation:hover {
  background: linear-gradient(#1e293b, #1e293b) padding-box,
              linear-gradient(90deg, #3b82f6, #3b82f6, #3b82f6, #3b82f6) border-box;
}

/* Enhanced Professional Sukli Animation */
.sukli-border-simple {
  position: relative;
  border: 2px solid transparent;
  border-radius: 0.375rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(147, 197, 253, 0.05));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

.sukli-border-simple::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 0.5rem;
  padding: 2px;
  background: linear-gradient(90deg, transparent, transparent, transparent, transparent);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask-composite: xor;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

.sukli-border-simple::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.375rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sukli-border-simple:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25), 0 2px 6px rgba(59, 130, 246, 0.15);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
}

.sukli-border-simple:hover::before {
  opacity: 1;
  background: linear-gradient(90deg, #3b82f6, transparent, transparent, transparent);
  animation: border-travel-enhanced 4s linear infinite;
}

.sukli-border-simple:hover::after {
  opacity: 1;
}

.sukli-border-simple:active {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease;
}

@keyframes border-travel-enhanced {
  0% {
    background: linear-gradient(90deg, #3b82f6 0%, rgba(59, 130, 246, 0.8) 20%, transparent 40%, transparent 100%);
  }
  25% {
    background: linear-gradient(180deg, transparent 0%, transparent 60%, rgba(59, 130, 246, 0.8) 80%, #3b82f6 100%);
  }
  50% {
    background: linear-gradient(270deg, transparent 0%, transparent 60%, rgba(59, 130, 246, 0.8) 80%, #3b82f6 100%);
  }
  75% {
    background: linear-gradient(0deg, transparent 0%, transparent 60%, rgba(59, 130, 246, 0.8) 80%, #3b82f6 100%);
  }
  100% {
    background: linear-gradient(90deg, #3b82f6 0%, rgba(59, 130, 246, 0.8) 20%, transparent 40%, transparent 100%);
  }
}

/* Pulse effect for extra attention */
@keyframes sukli-pulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25), 0 2px 6px rgba(59, 130, 246, 0.15);
  }
  50% {
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4), 0 4px 12px rgba(59, 130, 246, 0.25);
  }
}

/* Shimmer effect for the text */
@keyframes sukli-shimmer {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

.sukli-border-simple:hover .sukli-text {
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #3b82f6);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: sukli-shimmer 2s ease-in-out infinite;
}

/* Dark mode enhancements */
.dark .sukli-border-simple {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.05));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .sukli-border-simple:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 197, 253, 0.1));
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), 0 2px 6px rgba(59, 130, 246, 0.2);
}

/* ===================================================================== */
/* API GRAPHING CARDS TEXT VISIBILITY FIXES */
/* ===================================================================== */

/* Fix all text in API Graphing cards for light mode */
.light .card .text-blue-700,
.light .card .text-blue-800,
.light .card .text-green-700,
.light .card .text-green-800,
.light .card .text-yellow-700,
.light .card .text-yellow-800,
.light .card .text-purple-700,
.light .card .text-purple-800,
.light .card .text-indigo-700,
.light .card .text-indigo-800 {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

/* Fix light colored text in cards */
.light .card .text-blue-400,
.light .card .text-green-400,
.light .card .text-yellow-400,
.light .card .text-purple-400,
.light .card .text-indigo-400 {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* Fix very light text colors in cards */
.light .card .text-blue-300,
.light .card .text-green-300,
.light .card .text-yellow-300,
.light .card .text-purple-300,
.light .card .text-indigo-300 {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

/* Specific fixes for API Graphing card content */
.light .card p.text-xs {
  color: #374151 !important;
  font-weight: 500 !important;
}

.light .card span.text-sm {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

.light .card span.text-xs {
  color: #4b5563 !important;
  font-weight: 500 !important;
}

/* Fix gradient background text in light mode */
.light .bg-gradient-to-br .text-blue-700,
.light .bg-gradient-to-br .text-green-700,
.light .bg-gradient-to-br .text-yellow-700,
.light .bg-gradient-to-br .text-purple-700 {
  color: #ffffff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

.light .bg-gradient-to-br .text-blue-800,
.light .bg-gradient-to-br .text-green-800,
.light .bg-gradient-to-br .text-yellow-800,
.light .bg-gradient-to-br .text-purple-800 {
  color: #ffffff !important;
  font-weight: 700 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Fix Business Intelligence Summary card text */
.light .card h3.text-lg {
  color: #111827 !important;
  font-weight: 700 !important;
}

.light .card p.text-sm {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* Fix all card subtitle and description text */
.light .card .text-gray-500 {
  color: #6b7280 !important;
  font-weight: 500 !important;
}

.light .card .text-gray-400 {
  color: #9ca3af !important;
  font-weight: 500 !important;
}

/* Override any remaining light text in cards */
.light .card [class*="text-gray-1"],
.light .card [class*="text-slate-1"],
.light .card [class*="text-blue-1"],
.light .card [class*="text-green-1"],
.light .card [class*="text-yellow-1"],
.light .card [class*="text-purple-1"],
.light .card [class*="text-indigo-1"] {
  color: #1f2937 !important;
  font-weight: 600 !important;
}

/* Fix specific API Graphing card elements */
.light .card .text-blue-600 {
  color: #2563eb !important;
  font-weight: 600 !important;
}

.light .card .text-green-600 {
  color: #16a34a !important;
  font-weight: 600 !important;
}

.light .card .text-yellow-600 {
  color: #ca8a04 !important;
  font-weight: 600 !important;
}

.light .card .text-purple-600 {
  color: #9333ea !important;
  font-weight: 600 !important;
}

.light .card .text-indigo-600 {
  color: #4f46e5 !important;
  font-weight: 600 !important;
}

/* Ensure all card text is visible with high contrast */
.light .card h3 {
  color: #111827 !important;
  font-weight: 700 !important;
}

.light .card p {
  color: #374151 !important;
}

.light .card span {
  color: #4b5563 !important;
}

/* ===================================================================== */
/* PROFESSIONAL PRODUCT LISTS HEADER BUTTONS TEXT VISIBILITY FIX */
/* ===================================================================== */

/* Target only the specific problematic buttons in Product Lists */
.light .bg-gray-100.text-gray-700 {
  color: #4b5563 !important;
}

.light .bg-gray-100.text-gray-700:hover {
  color: #374151 !important;
}

/* Fix the red Delete button when products are selected */
.light .bg-red-100.text-red-700 {
  color: #dc2626 !important;
  font-weight: 600 !important;
}

.light .bg-red-100.text-red-700:hover {
  color: #991b1b !important;
}

/* ===================================================================== */
/* CALENDAR EVENT CARDS TEXT VISIBILITY FIX */
/* ===================================================================== */

/* Fix calendar event text visibility in light mode */
.light .bg-blue-100.text-blue-800 {
  color: #1e40af !important;
  font-weight: 500 !important;
}

.light .bg-purple-100.text-purple-800 {
  color: #7c3aed !important;
  font-weight: 500 !important;
}

.light .bg-yellow-100.text-yellow-800 {
  color: #d97706 !important;
  font-weight: 500 !important;
}

.light .bg-red-100.text-red-800 {
  color: #dc2626 !important;
  font-weight: 500 !important;
}

.light .bg-green-100.text-green-800 {
  color: #16a34a !important;
  font-weight: 500 !important;
}

.light .bg-gray-100.text-gray-800 {
  color: #374151 !important;
  font-weight: 500 !important;
}

/* ===================================================================== */
/* CALENDAR MOON PHASES BUTTON TEXT VISIBILITY FIX */
/* ===================================================================== */

/* Fix "Mga Hugis sa Bulan" button text visibility */
.light .bg-blue-50.text-blue-700 {
  color: #1d4ed8 !important;
  font-weight: 600 !important;
}

.light .text-gray-600 {
  color: #4b5563 !important;
  font-weight: 500 !important;
}
























